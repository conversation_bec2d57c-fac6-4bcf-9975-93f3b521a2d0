# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
bun.lockb

# Build outputs
dist/
dist-ssr/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Database files (will be created on server)
server/database/database.sqlite*
server/database/migrations/

# Logs
logs/
*.log

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Temporary files
tmp/
temp/
