// Simple setup script for VPS deployment
import bcrypt from 'bcryptjs';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function setupApp() {
  console.log('🚀 Setting up Lead App...');

  try {
    // Create database directory if it doesn't exist
    const dbDir = path.join(__dirname, 'server', 'database');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Initialize SQLite database
    const dbPath = path.join(dbDir, 'database.sqlite');
    const db = new Database(dbPath);

    // Enable foreign keys
    db.pragma('foreign_keys = ON');

    // Create users table (matching schema.ts)
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    `);

    // Create leads table (matching schema.ts)
    db.exec(`
      CREATE TABLE IF NOT EXISTS leads (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT,
        email TEXT,
        mobilenumber TEXT,
        website TEXT,
        latitude TEXT,
        longitude TEXT,
        address TEXT,
        city TEXT,
        state TEXT,
        postal_code TEXT,
        country TEXT,
        category TEXT,
        rating TEXT,
        review_count TEXT,
        closing_hour TEXT,
        facebookprofile TEXT,
        twitterprofile TEXT,
        linkedinprofile TEXT,
        instagramprofile TEXT,
        business_description TEXT,
        contacted INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create json_data table (matching schema.ts)
    db.exec(`
      CREATE TABLE IF NOT EXISTS json_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        file_name TEXT NOT NULL,
        original_data TEXT NOT NULL,
        data_type TEXT NOT NULL,
        record_count INTEGER NOT NULL DEFAULT 0,
        uploaded_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    console.log('✅ Database tables created');

    // Create admin user
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const adminName = process.env.ADMIN_NAME || 'Administrator';

    // Check if admin exists
    const existingAdmin = db.prepare('SELECT * FROM users WHERE email = ?').get(adminEmail);
    
    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash(adminPassword, 10);
      const now = Date.now();
      
      db.prepare(`
        INSERT INTO users (email, password, name, role, created_at, updated_at)
        VALUES (?, ?, ?, 'admin', ?, ?)
      `).run(adminEmail, hashedPassword, adminName, now, now);
      
      console.log('✅ Admin user created:');
      console.log('   Email:', adminEmail);
      console.log('   Password:', adminPassword);
      console.log('   ⚠️  Please change the password after first login!');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    db.close();
    console.log('🎉 Setup complete!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

setupApp();
