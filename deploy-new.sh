#!/bin/bash

echo "🚀 Complete Lead App Deployment with HTTPS..."
echo "🌐 Deploying to: https://lead.cybernox.tech"
echo "📁 Production-Ready Lead Management System"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

print_header() {
    echo -e "${CYAN}🎯 $1${NC}"
}

# Configuration
DOMAIN="lead.cybernox.tech"
APP_NAME="lead-app"
APP_PORT="3001"
PROJECT_DIR="$(pwd)"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project directory."
    exit 1
fi

print_header "Starting complete deployment for $DOMAIN"
print_info "Project directory: $PROJECT_DIR"
print_info "Target domain: $DOMAIN"
print_info "App port: $APP_PORT"
echo ""

# 1. Stop any conflicting web servers
print_step "Stopping conflicting web servers..."
systemctl stop lsws 2>/dev/null || true
/usr/local/lsws/bin/lswsctrl stop 2>/dev/null || true
pkill lshttpd 2>/dev/null || true
systemctl stop apache2 2>/dev/null || true
systemctl stop httpd 2>/dev/null || true
print_success "Conflicting web servers stopped"

# 2. Update system and install dependencies
print_step "Updating system and installing dependencies..."
apt update && apt upgrade -y
apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates

# 3. Install Node.js 18 if not installed
if ! command -v node &> /dev/null; then
    print_step "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi
print_info "Node.js version: $(node --version)"

# 4. Install nginx if not installed
if ! command -v nginx &> /dev/null; then
    print_step "Installing nginx..."
    apt install -y nginx
    systemctl enable nginx
fi

# 5. Install certbot if not installed
if ! command -v certbot &> /dev/null; then
    print_step "Installing certbot for SSL..."
    apt install -y certbot python3-certbot-nginx
fi

# 6. Install PM2 globally if not installed
if ! command -v pm2 &> /dev/null; then
    print_step "Installing PM2..."
    npm install -g pm2
fi

print_success "System dependencies installed"

# 7. Install project dependencies
print_step "Installing project dependencies..."
npm install --production=false
print_success "Project dependencies installed"

# 8. Build application
print_step "Building application..."
npm run build
print_success "Application built successfully"

# 9. Create environment configuration
print_step "Creating environment configuration..."
cat > .env << EOF
NODE_ENV=production
PORT=$APP_PORT
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_EMAIL=admin@$DOMAIN
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrator
EOF
print_success "Environment file created"

# 10. Fix route pattern in server/index.ts
print_step "Fixing route patterns..."
sed -i "s/app.get('\*'/app.get('\/\*'/g" server/index.ts
print_success "Route patterns fixed"

# 11. Fix database connection file (remove migration dependencies)
print_step "Fixing database connection..."
cat > server/database/connection.ts << 'EOF'
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create database connection
const sqlite = new Database(path.join(__dirname, 'database.sqlite'));
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('foreign_keys = ON');

// Create drizzle instance
export const db = drizzle(sqlite, { schema });

// Initialize database (no migrations needed - tables created by setup.js)
export function initializeDatabase() {
  try {
    // Test database connection
    const result = sqlite.prepare('SELECT 1 as test').get();
    if (result?.test === 1) {
      console.log('✅ Database connection initialized');
    } else {
      throw new Error('Database connection test failed');
    }

    // Database tables are already created by setup.js
    // No migrations needed
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export default db;
EOF
print_success "Database connection fixed"

# 11. Set up database and admin user
print_step "Setting up database and admin user..."
npm run setup
print_success "Database and admin user created"

# 11. Configure firewall (if available)
if command -v ufw &> /dev/null; then
    print_step "Configuring firewall..."
    ufw allow 22
    ufw allow 80
    ufw allow 443
    ufw --force enable
    print_success "Firewall configured"
else
    print_info "UFW not available, skipping firewall configuration"
fi

# 12. Create nginx configuration
print_step "Creating nginx configuration..."
rm -f /etc/nginx/sites-enabled/default

cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Serve static files from dist directory
    location / {
        root $PROJECT_DIR/dist;
        try_files \$uri \$uri/ /index.html;
        index index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Proxy API requests to the Node.js app
    location /api/ {
        proxy_pass http://localhost:$APP_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Handle file uploads
    client_max_body_size 50M;
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/

# Test nginx configuration
nginx -t
if [ $? -ne 0 ]; then
    print_error "Nginx configuration test failed"
    exit 1
fi

# Start nginx properly
systemctl stop nginx 2>/dev/null || true
pkill nginx 2>/dev/null || true
sleep 2
systemctl start nginx
print_success "Nginx configured and started"

# 13. Start the application with PM2
print_step "Starting application with PM2..."
pm2 delete $APP_NAME 2>/dev/null || true

# Test the app manually first
print_info "Testing application startup..."
timeout 10s npm run server:prod &
APP_PID=$!
sleep 5

# Check if the test startup worked
if kill -0 $APP_PID 2>/dev/null; then
    print_success "Application test startup successful"
    kill $APP_PID 2>/dev/null || true
else
    print_warning "Application test startup had issues, but continuing..."
fi

# Start with PM2
pm2 start npm --name $APP_NAME -- run server:prod
pm2 save
pm2 startup systemd -u root --hp /root
print_success "Application started with PM2"

# 14. Wait for app to start and test thoroughly
print_step "Testing application response..."
sleep 10

# Test if app is responding
APP_RESPONDING=false
for i in {1..10}; do
    if curl -f http://localhost:$APP_PORT/api/health &>/dev/null; then
        print_success "✅ Application is responding on port $APP_PORT"
        APP_RESPONDING=true
        break
    elif curl -f http://localhost:$APP_PORT/ &>/dev/null; then
        print_success "✅ Application is responding on port $APP_PORT (main route)"
        APP_RESPONDING=true
        break
    else
        print_warning "Attempt $i/10: Application not responding yet, waiting..."
        sleep 3
    fi
done

if [ "$APP_RESPONDING" = false ]; then
    print_error "Application is not responding after multiple attempts"
    print_info "Checking PM2 logs..."
    pm2 logs $APP_NAME --lines 20
    print_info "You may need to check the logs and restart manually"
fi

# 15. Get SSL certificate
print_step "Obtaining SSL certificate..."
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN --redirect || {
    print_warning "SSL certificate setup failed, but app is running on HTTP"
}

# 16. Set up SSL auto-renewal
print_step "Setting up SSL auto-renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
print_success "SSL auto-renewal configured"

# 17. Final testing and diagnostics
print_step "Running final tests..."

# Test the website through nginx
if curl -f http://$DOMAIN &>/dev/null; then
    print_success "✅ Website is accessible via HTTP"
else
    print_warning "⚠️  Website not accessible via HTTP"
fi

if curl -f https://$DOMAIN &>/dev/null; then
    print_success "✅ Website is accessible via HTTPS"
else
    print_warning "⚠️  Website not accessible via HTTPS (SSL may not be configured)"
fi

# Check what's actually running
print_info "Current system status:"
echo "  🔹 PM2 Status:"
pm2 status
echo "  🔹 Nginx Status:"
systemctl status nginx --no-pager -l
echo "  🔹 Port $APP_PORT Status:"
netstat -tlnp | grep :$APP_PORT || echo "    No process listening on port $APP_PORT"
echo "  🔹 Port 80 Status:"
netstat -tlnp | grep :80 || echo "    No process listening on port 80"

# 18. Display final information
echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Application Details:"
echo "🌐 URL: https://$DOMAIN"
echo "👤 Admin Email: admin@$DOMAIN"
echo "🔑 Admin Password: admin123"
echo ""
echo "🔧 Management Commands:"
echo "  📊 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs $APP_NAME"
echo "  🔄 Restart App: pm2 restart $APP_NAME"
echo "  🌐 Check Nginx: systemctl status nginx"
echo "  🔒 Check SSL: sudo certbot certificates"
echo ""
echo "🆘 Troubleshooting:"
echo "  📋 App Logs: pm2 logs $APP_NAME --lines 50"
echo "  🌐 Nginx Logs: tail -f /var/log/nginx/$DOMAIN.error.log"
echo "  🔍 Test Local: curl http://localhost:$APP_PORT"
echo "  🔍 Test Remote: curl http://$DOMAIN"
echo ""
if [ "$APP_RESPONDING" = true ]; then
    echo "✅ Your Lead App is running successfully at: https://$DOMAIN"
else
    echo "⚠️  Your Lead App deployment completed but may need troubleshooting"
    echo "   Check the logs above and run: pm2 logs $APP_NAME"
fi
echo ""
echo "⚠️  IMPORTANT: Change the admin password after first login!"
