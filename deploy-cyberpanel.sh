#!/bin/bash

echo "🚀 Lead App Deployment for CyberPanel"
echo "🌐 Deploying to: https://lead.cybernox.tech"
echo "🔧 Optimized for CyberPanel + LiteSpeed"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Configuration
DOMAIN="lead.cybernox.tech"
APP_NAME="lead-app"
APP_PORT="3001"
PROJECT_DIR="$(pwd)"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

print_info "Starting CyberPanel deployment for $DOMAIN"
print_info "Project directory: $PROJECT_DIR"
echo ""

# 1. Install Node.js if not installed
print_step "Installing Node.js 18..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi
print_info "Node.js version: $(node --version)"

# 2. Install PM2 if not installed
if ! command -v pm2 &> /dev/null; then
    print_step "Installing PM2..."
    npm install -g pm2
fi
print_success "System dependencies ready"

# 3. Create missing config files
print_step "Creating missing config files..."

# Create tsconfig.app.json
cat > tsconfig.app.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
EOF

# Create tsconfig.node.json
cat > tsconfig.node.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "noEmit": true
  },
  "include": ["vite.config.ts"]
}
EOF

# Create vite.config.ts
cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
  },
})
EOF

print_success "Config files created"

# 4. Install dependencies
print_step "Installing project dependencies..."
npm install --production=false
print_success "Dependencies installed"

# 5. Fix server issues
print_step "Fixing server configuration..."

# Fix route patterns
sed -i "s/app\.get('\*'/app.get('\/\*'/g" server/index.ts

# Fix database connection
cat > server/database/connection.ts << 'EOF'
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create database connection
const sqlite = new Database(path.join(__dirname, 'database.sqlite'));
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('foreign_keys = ON');

// Create drizzle instance
export const db = drizzle(sqlite, { schema });

// Initialize database (no migrations needed)
export function initializeDatabase() {
  try {
    const result = sqlite.prepare('SELECT 1 as test').get();
    if (result?.test === 1) {
      console.log('✅ Database connection initialized');
    } else {
      throw new Error('Database connection test failed');
    }
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export default db;
EOF

print_success "Server configuration fixed"

# 6. Build application
print_step "Building React application..."
npm run build
print_success "Application built successfully"

# 7. Setup database
print_step "Setting up database..."
npm run setup
print_success "Database setup complete"

# 8. Create environment file
print_step "Creating environment configuration..."
cat > .env << EOF
NODE_ENV=production
PORT=$APP_PORT
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_EMAIL=admin@$DOMAIN
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrator
EOF
print_success "Environment configured"

# 9. Stop existing PM2 processes
print_step "Managing PM2 processes..."
pm2 delete $APP_NAME 2>/dev/null || true

# 10. Start application with PM2
print_step "Starting Node.js application..."
pm2 start npm --name $APP_NAME -- run server:prod
pm2 save
pm2 startup systemd -u root --hp /root
print_success "Application started with PM2"

# 11. Test application
print_step "Testing application..."
sleep 10

APP_RESPONDING=false
for i in {1..5}; do
    if curl -f http://localhost:$APP_PORT/api/health &>/dev/null; then
        print_success "✅ Application responding on port $APP_PORT"
        APP_RESPONDING=true
        break
    else
        print_warning "Attempt $i: Waiting for app to start..."
        sleep 5
    fi
done

if [ "$APP_RESPONDING" = false ]; then
    print_error "Application not responding. Check logs with: pm2 logs $APP_NAME"
fi

# 12. Display configuration instructions
echo ""
echo "🎉 Application deployment completed!"
echo ""
echo "📋 Next Steps for CyberPanel:"
echo ""
echo "1. 📁 Copy the built files to your domain's public_html:"
echo "   cp -r dist/* /home/<USER>/public_html/"
echo ""
echo "2. 🔧 Update your vhost configuration in CyberPanel:"
echo "   - Go to CyberPanel > Websites > List Websites"
echo "   - Click on lead.cybernox.tech"
echo "   - Go to 'Manage' > 'Rewrite Rules'"
echo "   - Use the configuration from: lead.cybernox.tech-vhost.conf"
echo ""
echo "3. 🔄 Restart LiteSpeed:"
echo "   systemctl restart lsws"
echo ""
echo "📋 Application Details:"
echo "🌐 URL: https://$DOMAIN"
echo "👤 Admin Email: admin@$DOMAIN"
echo "🔑 Admin Password: admin123"
echo "🔧 Node.js App: Running on port $APP_PORT"
echo ""
echo "🔧 Management Commands:"
echo "  📊 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs $APP_NAME"
echo "  🔄 Restart App: pm2 restart $APP_NAME"
echo ""
echo "⚠️  IMPORTANT:"
echo "1. Apply the vhost configuration in CyberPanel"
echo "2. Copy dist files to public_html"
echo "3. Restart LiteSpeed"
echo "4. Change admin password after first login"
echo ""
print_success "Deployment ready for CyberPanel configuration!"
