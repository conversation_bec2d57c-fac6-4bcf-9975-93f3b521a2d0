docRoot                   $VH_ROOT/public_html/dist
vhDomain                  $VH_NAME
vhAliases                 www.$VH_NAME
adminEmails               <EMAIL>
enableGzip                1
enableIpGeo               1

index  {
  useServer               0
  indexFiles              index.html, index.js
}

errorlog $VH_ROOT/logs/$VH_NAME.error_log {
  useServer               0
  logLevel                WARN
  rollingSize             10M
}

accesslog $VH_ROOT/logs/$VH_NAME.access_log {
  useServer               0
  logFormat               "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\""
  logHeaders              5
  rollingSize             10M
  keepDays                10  
  compressArchive         1
}

phpIniOverride  {
}

module cache {
  storagePath /usr/local/lsws/cachedata/$VH_NAME
}

rewrite  {
  enable                  1
  autoLoadHtaccess        1
  
  rules                   <<<END_rules
  # Handle React Router - serve index.html for all non-API routes
  RewriteCond %{REQUEST_URI} !^/api/
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule ^(.*)$ /index.html [L]
  END_rules
}

context /.well-known/acme-challenge {
  location                /usr/local/lsws/Example/html/.well-known/acme-challenge
  allowBrowse             1

  rewrite  {
    enable                0
  }
  addDefaultCharset       off

  phpIniOverride  {
  }
}

# ---- Node.js API Proxy Configuration ----

extprocessor leadapp-nodejs {
  type                    proxy
  address                 127.0.0.1:3001
  maxConns                100
  pcKeepAliveTimeout      60
  initTimeout             60
  retryTimeout            0
  respBuffer              0
}

# API routes proxy to Node.js
context /api/ {
  type                    proxy
  uri                     http://127.0.0.1:3001/api/
  extraHeaders            <<<END_extraHeaders
  Host $host
  X-Real-IP $remote_addr
  X-Forwarded-For $proxy_add_x_forwarded_for
  X-Forwarded-Proto $scheme
  END_extraHeaders
  addDefaultCharset       off

  rewrite  {
    enable                0
  }

  phpIniOverride  {
  }
}

# Static files context (React app)
context / {
  type                    docroot
  location                $VH_ROOT/public_html/dist/
  allowBrowse             1
  indexFiles              index.html
  addDefaultCharset       off

  rewrite  {
    enable                1
    inherit               1
  }

  phpIniOverride  {
  }
}

# Handle file uploads
context /uploads/ {
  type                    proxy
  uri                     http://127.0.0.1:3001/uploads/
  extraHeaders            <<<END_extraHeaders
  Host $host
  X-Real-IP $remote_addr
  X-Forwarded-For $proxy_add_x_forwarded_for
  X-Forwarded-Proto $scheme
  END_extraHeaders
  addDefaultCharset       off

  phpIniOverride  {
  }
}

vhssl  {
  keyFile                 /etc/letsencrypt/live/lead.cybernox.tech/privkey.pem
  certFile                /etc/letsencrypt/live/lead.cybernox.tech/fullchain.pem
  certChain               1
  sslProtocol             24
  enableECDHE             1
  renegProtection         1
  sslSessionCache         1
  enableSpdy              15
  enableStapling          1
  ocspRespMaxAge          86400
}
