# 🚀 Lead App - CyberPanel Setup Guide

## 📋 Complete Setup for CyberPanel + LiteSpeed

### **Step 1: Deploy the Application**

```bash
# Run the CyberPanel deployment script
chmod +x deploy-cyberpanel.sh
sudo ./deploy-cyberpanel.sh
```

### **Step 2: Copy Files to Public HTML**

```bash
# Copy built React app to your domain's public_html
cp -r dist/* /home/<USER>/public_html/

# Set correct permissions
chown -R lead.cybernox.tech:lead.cybernox.tech /home/<USER>/public_html/
chmod -R 755 /home/<USER>/public_html/
```

### **Step 3: Configure Virtual Host in CyberPanel**

1. **Login to CyberPanel**
   - Go to your CyberPanel admin panel
   - Login with your admin credentials

2. **Navigate to Website Management**
   - Go to `Websites` > `List Websites`
   - Find `lead.cybernox.tech` and click `Manage`

3. **Update Virtual Host Configuration**
   - Go to `Configurations` > `Edit Configuration`
   - Replace the entire vhost configuration with the content from `lead.cybernox.tech-vhost.conf`

4. **Key Configuration Points:**
   ```
   docRoot: $VH_ROOT/public_html/dist
   
   # API Proxy Context
   context /api/ {
     type: proxy
     uri: http://127.0.0.1:3001/api/
   }
   
   # Static Files Context  
   context / {
     type: docroot
     location: $VH_ROOT/public_html/dist/
   }
   ```

### **Step 4: Configure Rewrite Rules**

In CyberPanel:
1. Go to `Websites` > `List Websites`
2. Click on `lead.cybernox.tech`
3. Go to `Rewrite Rules`
4. Add these rules:

```apache
# Handle React Router - serve index.html for all non-API routes
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]
```

### **Step 5: SSL Configuration**

1. **In CyberPanel:**
   - Go to `SSL` > `Manage SSL`
   - Select `lead.cybernox.tech`
   - Click `Issue SSL` (Let's Encrypt)

2. **Or manually update vhost SSL section:**
   ```
   vhssl {
     keyFile: /etc/letsencrypt/live/lead.cybernox.tech/privkey.pem
     certFile: /etc/letsencrypt/live/lead.cybernox.tech/fullchain.pem
     certChain: 1
   }
   ```

### **Step 6: Restart Services**

```bash
# Restart LiteSpeed
systemctl restart lsws

# Check PM2 status
pm2 status

# Test the application
curl http://localhost:3001/api/health
```

### **Step 7: Test Your Application**

1. **Visit your website:**
   - `https://lead.cybernox.tech`

2. **Login with admin credentials:**
   - Email: `<EMAIL>`
   - Password: `admin123`

3. **Test functionality:**
   - Upload JSON files
   - Manage leads
   - Check all features work

## 🔧 **How It Works:**

### **File Structure:**
```
/home/<USER>/
├── public_html/
│   ├── dist/              # React app (served by LiteSpeed)
│   │   ├── index.html
│   │   ├── assets/
│   │   └── ...
│   ├── server/            # Node.js backend
│   ├── package.json
│   └── ...
└── logs/                  # LiteSpeed logs
```

### **Request Flow:**
1. **Static Files** (`/`, `/assets/*`) → LiteSpeed serves from `dist/`
2. **API Requests** (`/api/*`) → LiteSpeed proxies to Node.js (port 3001)
3. **React Router** → All non-API routes serve `index.html`

## 🎯 **Management Commands:**

```bash
# Check Node.js app status
pm2 status
pm2 logs lead-app

# Restart Node.js app
pm2 restart lead-app

# Restart LiteSpeed
systemctl restart lsws

# Check LiteSpeed status
systemctl status lsws

# View LiteSpeed logs
tail -f /home/<USER>/logs/lead.cybernox.tech.error_log
```

## 🆘 **Troubleshooting:**

### **If API calls fail:**
1. Check PM2 logs: `pm2 logs lead-app`
2. Verify Node.js app is running: `curl http://localhost:3001/api/health`
3. Check vhost proxy configuration

### **If static files don't load:**
1. Verify files in `/home/<USER>/public_html/dist/`
2. Check file permissions
3. Verify docRoot in vhost config

### **If SSL doesn't work:**
1. Check SSL certificate paths in vhost
2. Verify certificates exist: `ls -la /etc/letsencrypt/live/lead.cybernox.tech/`
3. Restart LiteSpeed: `systemctl restart lsws`

## ✅ **Final Result:**

Your Lead App will be running with:
- ✅ **Frontend**: React app served by LiteSpeed
- ✅ **Backend**: Node.js API on port 3001
- ✅ **Database**: SQLite with admin user
- ✅ **SSL**: HTTPS certificate
- ✅ **Management**: PM2 process management

**URL**: `https://lead.cybernox.tech`
**Admin**: `<EMAIL>` / `admin123`

🎉 **Perfect integration with CyberPanel!**
