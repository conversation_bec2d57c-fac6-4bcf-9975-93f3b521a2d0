# 🚀 Lead App - Complete Installation Guide

## 📦 Production-Ready Lead Management System

This is a complete, fixed version of your Lead App with all issues resolved.

### 🎯 What's Fixed

✅ **Database Issues**
- Fixed Drizzle migration errors
- Proper SQLite database setup
- Matching schema and table structure
- No migration dependencies

✅ **Server Configuration**
- Fixed nginx configuration for static files + API proxy
- Proper PM2 process management
- Environment variable handling
- HTTPS/SSL certificate setup

✅ **Application Structure**
- Clean package.json with working scripts
- Fixed TypeScript compilation
- Proper build process
- Production optimizations

### 🚀 Installation Steps

#### 1. **Prepare Your Server**
- VPS with Ubuntu 20.04+ or similar Linux
- Root access (sudo)
- Domain `lead.cybernox.tech` pointing to your VPS IP

#### 2. **Upload and Extract**
```bash
# Upload your project zip to VPS
# Extract to your desired location
unzip lead-json-display-main.zip
cd lead-json-display-main
```

#### 3. **Replace Files (IMPORTANT)**
Before running the deployment, replace these files with the fixed versions:

```bash
# Replace the deploy script
mv deploy-new.sh deploy.sh

# Replace the database connection file
mv server/database/connection-new.ts server/database/connection.ts

# Make deploy script executable
chmod +x deploy.sh
```

#### 4. **Run Deployment**
```bash
# Run the complete deployment (as root)
sudo ./deploy.sh
```

### 🎉 What the Script Does

✅ **System Setup**
- Stops conflicting web servers (LiteSpeed, Apache)
- Installs Node.js 18, nginx, certbot, PM2
- Updates system packages

✅ **Application Setup**
- Installs dependencies with `npm install`
- Builds React app with `npm run build`
- Creates database with `npm run setup`
- Configures environment variables

✅ **Server Configuration**
- Configures nginx to serve static files from `dist/`
- Proxies API requests to Node.js app on port 3001
- Obtains free SSL certificate
- Sets up auto-restart on reboot

✅ **Testing & Diagnostics**
- Tests application startup
- Verifies HTTP/HTTPS access
- Provides detailed status information
- Shows troubleshooting commands

### 🌐 After Installation

Your app will be running at:
- **HTTPS**: `https://lead.cybernox.tech` ✅
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `admin123`

### 🔧 How It Works

**Frontend (React)**
- Served directly by nginx from `/dist` folder
- No port 3001 needed for users
- Fast static file serving with caching

**Backend (Node.js)**
- Runs on port 3001 (internal only)
- Handles API requests via nginx proxy
- SQLite database with proper schema
- JWT authentication

**Web Server (nginx)**
- Serves static files: `location /`
- Proxies API: `location /api/`
- SSL termination
- File upload handling

### 🎯 Features Ready to Use

- ✅ User authentication system
- ✅ JSON lead upload and processing
- ✅ Lead management and tracking
- ✅ Mark leads as contacted
- ✅ Previous uploads history
- ✅ Admin dashboard
- ✅ Database persistence
- ✅ HTTPS security
- ✅ Auto-restart on server reboot

### 🔧 Management Commands

```bash
# Check app status
pm2 status

# View app logs
pm2 logs lead-app

# Restart app
pm2 restart lead-app

# Check nginx
systemctl status nginx

# Check SSL certificate
sudo certbot certificates

# Test locally
curl http://localhost:3001/api/health

# Test remotely
curl https://lead.cybernox.tech
```

### 🆘 Troubleshooting

If you encounter issues:

1. **Check app logs**: `pm2 logs lead-app --lines 50`
2. **Check nginx logs**: `tail -f /var/log/nginx/lead.cybernox.tech.error.log`
3. **Test database**: `ls -la server/database/`
4. **Test build**: `ls -la dist/`
5. **Restart services**: `pm2 restart lead-app && systemctl restart nginx`

### 🔄 Updates

To update the app:
1. Upload new version
2. Extract and replace files
3. Run `sudo ./deploy.sh` again

### 📋 File Structure

```
lead-json-display-main/
├── deploy.sh                 # Fixed deployment script
├── setup.js                  # Database setup script
├── package.json              # Dependencies and scripts
├── dist/                     # Built React app (created by build)
├── server/
│   ├── index.ts              # Express server
│   ├── database/
│   │   ├── connection.ts     # Fixed database connection
│   │   ├── schema.ts         # Database schema
│   │   └── database.sqlite   # SQLite database (created by setup)
│   ├── routes/               # API routes
│   └── middleware/           # Auth middleware
└── src/                      # React source code
```

### ⚠️ Important Notes

1. **Change admin password** after first login
2. **Backup database** regularly: `cp server/database/database.sqlite backup/`
3. **Monitor logs** for any issues: `pm2 logs lead-app`
4. **SSL renewal** is automatic via cron job

**Your Lead App is now 100% ready for production!** 🚀

---

**Need help?** Check the deployment logs or run the troubleshooting commands above.
