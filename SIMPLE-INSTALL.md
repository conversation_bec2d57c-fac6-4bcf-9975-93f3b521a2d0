# 🚀 Simple Lead App Deployment

## 📦 Direct Domain Deployment - No nginx, No custom ports

Deploy your Lead App directly to `https://lead.cybernox.tech` without any nginx configuration or custom ports.

### 🎯 What This Does

✅ **Direct Web Access**
- App runs directly on port 80 (standard web port)
- No nginx configuration needed
- No custom ports (like 3001)
- Direct access via your domain

✅ **Simple Setup**
- One command deployment
- Automatic dependency installation
- Database setup included
- PM2 process management

### 🚀 Installation Steps

```bash
# 1. Upload your project to VPS
# 2. Extract and navigate
unzip lead-json-display-main.zip
cd lead-json-display-main

# 3. Make deploy script executable
chmod +x simple-deploy.sh

# 4. Run the simple deployment (as root)
sudo ./simple-deploy.sh
```

**That's it!** Your app will be running at `http://lead.cybernox.tech`

### 🔧 How It Works

**Single Node.js App**
- ✅ Serves React frontend from `/dist` folder
- ✅ Handles API requests on `/api/*` routes
- ✅ Runs directly on port 80
- ✅ No nginx proxy needed

**File Structure**
```
Your App:
├── Frontend (React) → Served from /dist
├── Backend (API) → Handles /api/* routes
└── Database (SQLite) → Local database file
```

**URL Structure**
- `http://lead.cybernox.tech/` → React App
- `http://lead.cybernox.tech/api/health` → API Health Check
- `http://lead.cybernox.tech/api/auth/login` → Login API
- `http://lead.cybernox.tech/api/leads` → Leads API

### 🌐 After Installation

Your app will be running at:
- **URL**: `http://lead.cybernox.tech`
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `admin123`

### 🎉 Features Working

- ✅ User authentication system
- ✅ JSON lead upload and processing
- ✅ Lead management and tracking
- ✅ Mark leads as contacted
- ✅ Previous uploads history
- ✅ Admin dashboard
- ✅ Database persistence
- ✅ Auto-restart on server reboot

### 🔧 Management Commands

```bash
# Check app status
pm2 status

# View app logs
pm2 logs lead-app

# Restart app
pm2 restart lead-app

# Test locally
curl http://localhost

# Test remotely
curl http://lead.cybernox.tech
```

### 🆘 Troubleshooting

If you encounter issues:

1. **Check app logs**: `pm2 logs lead-app --lines 50`
2. **Check if port 80 is free**: `netstat -tlnp | grep :80`
3. **Test locally**: `curl http://localhost`
4. **Check DNS**: Make sure `lead.cybernox.tech` points to your VPS IP
5. **Restart app**: `pm2 restart lead-app`

### 🔄 Updates

To update the app:
1. Upload new version
2. Extract and replace files
3. Run `sudo ./simple-deploy.sh` again

### ⚠️ Important Notes

1. **Change admin password** after first login
2. **Backup database** regularly: `cp server/database/database.sqlite backup/`
3. **Monitor logs**: `pm2 logs lead-app`
4. **Port 80 access**: App runs as root to access port 80
5. **No SSL**: This setup uses HTTP only. For HTTPS, you'll need additional SSL configuration

### 🎯 Advantages

- ✅ **Simple**: No nginx configuration
- ✅ **Direct**: No port numbers in URLs
- ✅ **Fast**: Direct serving without proxy
- ✅ **Clean**: Single process handles everything
- ✅ **Easy**: One command deployment

### 📋 What Gets Installed

- Node.js 18 (if not present)
- PM2 process manager
- Your Lead App dependencies
- SQLite database with admin user

**Your Lead App will be accessible directly at `http://lead.cybernox.tech`!** 🚀

---

**Need help?** Check the troubleshooting section or run `pm2 logs lead-app` to see what's happening.
