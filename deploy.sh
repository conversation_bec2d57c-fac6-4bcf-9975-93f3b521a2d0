#!/bin/bash

echo "🚀 Complete Lead App Deployment with HTTPS..."
echo "🌐 Deploying to: https://lead.cybernox.tech"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Configuration
DOMAIN="lead.cybernox.tech"
APP_NAME="lead-app"
APP_PORT="3001"
PROJECT_DIR="/home/<USER>"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project directory."
    exit 1
fi

print_info "Starting complete deployment for $DOMAIN"
print_info "Project directory: $(pwd)"

# 1. Stop any conflicting web servers
print_step "Checking for conflicting web servers..."
# Stop LiteSpeed if running
systemctl stop lsws 2>/dev/null || true
/usr/local/lsws/bin/lswsctrl stop 2>/dev/null || true
pkill lshttpd 2>/dev/null || true
# Stop Apache if running
systemctl stop apache2 2>/dev/null || true
systemctl stop httpd 2>/dev/null || true
print_success "Conflicting web servers stopped"

# 2. Update system and install dependencies
print_step "Updating system and installing dependencies..."
apt update && apt upgrade -y
apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates

# 3. Install Node.js 18 if not installed
if ! command -v node &> /dev/null; then
    print_step "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi
print_info "Node.js version: $(node --version)"

# 4. Install nginx if not installed
if ! command -v nginx &> /dev/null; then
    print_step "Installing nginx..."
    apt install -y nginx
    systemctl enable nginx
fi

# 5. Install certbot if not installed
if ! command -v certbot &> /dev/null; then
    print_step "Installing certbot for SSL..."
    apt install -y certbot python3-certbot-nginx
fi

# 6. Install PM2 globally if not installed
if ! command -v pm2 &> /dev/null; then
    print_step "Installing PM2..."
    npm install -g pm2
fi

print_success "System dependencies installed"

# 6. Install project dependencies
print_warning "Installing project dependencies..."
npm install
if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi
print_success "Project dependencies installed"

# 7. Build the application
print_warning "Building application..."
npm run build
if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi
print_success "Application built successfully"

# 8. Create .env file
print_warning "Creating environment configuration..."
if [ ! -f ".env" ]; then
    cat > .env << EOF
NODE_ENV=production
PORT=$APP_PORT
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_EMAIL=admin@$DOMAIN
ADMIN_PASSWORD=admin123
EOF
    print_success "Environment file created"
else
    print_info "Environment file already exists"
fi

# 9. Setup database and admin user
print_warning "Setting up database and admin user..."
npm run setup
if [ $? -ne 0 ]; then
    print_error "Database setup failed"
    exit 1
fi
print_success "Database and admin user created"

# 10. Configure firewall (only if ufw is available)
if command -v ufw &> /dev/null; then
    print_warning "Configuring firewall..."
    ufw allow 22
    ufw allow 80
    ufw allow 443
    ufw --force enable
    print_success "Firewall configured"
else
    print_info "UFW not available, skipping firewall configuration"
fi

# 11. Create nginx configuration (HTTP first)
print_warning "Creating nginx configuration..."
cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;

    location / {
        proxy_pass http://localhost:$APP_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
nginx -t
if [ $? -ne 0 ]; then
    print_error "Nginx configuration test failed"
    exit 1
fi

# Start nginx properly
systemctl stop nginx 2>/dev/null || true
pkill nginx 2>/dev/null || true
systemctl start nginx
print_success "Nginx configured and started"

# 12. Start the application with PM2
print_step "Starting application with PM2..."
pm2 delete $APP_NAME 2>/dev/null || true

# Test the app manually first
print_info "Testing application startup..."
timeout 10s npx tsx server/index.ts &
APP_PID=$!
sleep 5

# Check if the test startup worked
if kill -0 $APP_PID 2>/dev/null; then
    print_success "Application test startup successful"
    kill $APP_PID 2>/dev/null || true
else
    print_warning "Application test startup had issues, but continuing..."
fi

# Start with PM2
pm2 start "npx tsx server/index.ts" --name $APP_NAME
pm2 save
pm2 startup systemd -u root --hp /root
print_success "Application started with PM2"

# 13. Wait for app to start and test thoroughly
print_step "Testing application response..."
sleep 10

# Test if app is responding
APP_RESPONDING=false
for i in {1..10}; do
    if curl -f http://localhost:$APP_PORT/api/health &>/dev/null; then
        print_success "✅ Application is responding on port $APP_PORT"
        APP_RESPONDING=true
        break
    elif curl -f http://localhost:$APP_PORT/ &>/dev/null; then
        print_success "✅ Application is responding on port $APP_PORT (main route)"
        APP_RESPONDING=true
        break
    else
        print_warning "Attempt $i/10: Application not responding yet, waiting..."
        sleep 3
    fi
done

if [ "$APP_RESPONDING" = false ]; then
    print_error "Application is not responding after multiple attempts"
    print_info "Checking PM2 logs..."
    pm2 logs $APP_NAME --lines 20
    print_info "You may need to check the logs and restart manually"
fi

# 14. Get SSL certificate
print_warning "Obtaining SSL certificate..."
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN --redirect
if [ $? -eq 0 ]; then
    print_success "SSL certificate obtained and configured"
else
    print_warning "SSL certificate setup failed, but app is running on HTTP"
fi

# 15. Set up auto-renewal
print_warning "Setting up SSL auto-renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
print_success "SSL auto-renewal configured"

# 16. Final testing and diagnostics
print_step "Running final tests..."

# Test the website through nginx
if curl -f http://$DOMAIN &>/dev/null; then
    print_success "✅ Website is accessible via HTTP"
else
    print_warning "⚠️  Website not accessible via HTTP"
fi

if curl -f https://$DOMAIN &>/dev/null; then
    print_success "✅ Website is accessible via HTTPS"
else
    print_warning "⚠️  Website not accessible via HTTPS (SSL may not be configured)"
fi

# Check what's actually running
print_info "Current system status:"
echo "  🔹 PM2 Status:"
pm2 status
echo "  🔹 Nginx Status:"
systemctl status nginx --no-pager -l
echo "  🔹 Port 3001 Status:"
netstat -tlnp | grep :3001 || echo "    No process listening on port 3001"
echo "  🔹 Port 80 Status:"
netstat -tlnp | grep :80 || echo "    No process listening on port 80"

# 17. Display final information
echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Application Details:"
echo "🌐 URL: https://$DOMAIN"
echo "👤 Admin Email: admin@$DOMAIN"
echo "🔑 Admin Password: admin123"
echo ""
echo "� Management Commands:"
echo "  📊 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs $APP_NAME"
echo "  🔄 Restart App: pm2 restart $APP_NAME"
echo "  🌐 Check Nginx: systemctl status nginx"
echo "  🔒 Check SSL: sudo certbot certificates"
echo ""
echo "🆘 Troubleshooting:"
echo "  📋 App Logs: pm2 logs $APP_NAME --lines 50"
echo "  🌐 Nginx Logs: tail -f /var/log/nginx/$DOMAIN.error.log"
echo "  🔍 Test Local: curl http://localhost:3001"
echo "  🔍 Test Remote: curl http://$DOMAIN"
echo ""
if [ "$APP_RESPONDING" = true ]; then
    echo "✅ Your Lead App is running successfully at: https://$DOMAIN"
else
    echo "⚠️  Your Lead App deployment completed but may need troubleshooting"
    echo "   Check the logs above and run: pm2 logs $APP_NAME"
fi
echo ""
echo "⚠️  IMPORTANT: Change the admin password after first login!"
