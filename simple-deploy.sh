#!/bin/bash

echo "🚀 Simple Lead App Deployment"
echo "🌐 Direct deployment to: https://lead.cybernox.tech"
echo "📁 No nginx, No custom ports - Direct web access"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Configuration
DOMAIN="lead.cybernox.tech"
APP_NAME="lead-app"
PROJECT_DIR="$(pwd)"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project directory."
    exit 1
fi

print_info "Starting simple deployment for $DOMAIN"
print_info "Project directory: $PROJECT_DIR"
echo ""

# 1. Install Node.js if not installed
if ! command -v node &> /dev/null; then
    print_step "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi
print_info "Node.js version: $(node --version)"

# 2. Install PM2 globally if not installed
if ! command -v pm2 &> /dev/null; then
    print_step "Installing PM2..."
    npm install -g pm2
fi

print_success "System dependencies ready"

# 3. Install project dependencies
print_step "Installing project dependencies..."
npm install
print_success "Project dependencies installed"

# 4. Build application
print_step "Building application..."
npm run build
print_success "Application built successfully"

# 5. Create environment configuration for port 80
print_step "Creating environment configuration..."
cat > .env << EOF
NODE_ENV=production
PORT=80
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_EMAIL=admin@$DOMAIN
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrator
EOF
print_success "Environment file created"

# 6. Fix route pattern in server/index.ts
print_step "Fixing route patterns..."
sed -i "s/app.get('\*'/app.get('\/\*'/g" server/index.ts
print_success "Route patterns fixed"

# 7. Fix database connection file (remove migration dependencies)
print_step "Fixing database connection..."
cat > server/database/connection.ts << 'EOF'
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__dirname);

// Create database connection
const sqlite = new Database(path.join(__dirname, 'database.sqlite'));
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('foreign_keys = ON');

// Create drizzle instance
export const db = drizzle(sqlite, { schema });

// Initialize database (no migrations needed - tables created by setup.js)
export function initializeDatabase() {
  try {
    // Test database connection
    const result = sqlite.prepare('SELECT 1 as test').get();
    if (result?.test === 1) {
      console.log('✅ Database connection initialized');
    } else {
      throw new Error('Database connection test failed');
    }

    // Database tables are already created by setup.js
    // No migrations needed
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export default db;
EOF
print_success "Database connection fixed"

# 7. Set up database and admin user
print_step "Setting up database and admin user..."
npm run setup
print_success "Database and admin user created"

# 7. Stop any existing processes on port 80
print_step "Stopping any processes on port 80..."
fuser -k 80/tcp 2>/dev/null || true
systemctl stop nginx 2>/dev/null || true
systemctl stop apache2 2>/dev/null || true
print_success "Port 80 cleared"

# 8. Start the application with PM2 on port 80
print_step "Starting application on port 80..."
pm2 delete $APP_NAME 2>/dev/null || true

# Start with PM2 on port 80
pm2 start npm --name $APP_NAME -- run server:prod
pm2 save
pm2 startup systemd -u root --hp /root
print_success "Application started with PM2"

# 9. Wait for app to start and test
print_step "Testing application response..."
sleep 10

# Test if app is responding on port 80
APP_RESPONDING=false
for i in {1..10}; do
    if curl -f http://localhost/api/health &>/dev/null; then
        print_success "✅ Application is responding on port 80"
        APP_RESPONDING=true
        break
    elif curl -f http://localhost/ &>/dev/null; then
        print_success "✅ Application is responding on port 80 (main route)"
        APP_RESPONDING=true
        break
    else
        print_warning "Attempt $i/10: Application not responding yet, waiting..."
        sleep 3
    fi
done

if [ "$APP_RESPONDING" = false ]; then
    print_error "Application is not responding after multiple attempts"
    print_info "Checking PM2 logs..."
    pm2 logs $APP_NAME --lines 20
    print_info "You may need to check the logs and restart manually"
fi

# 10. Test external access
print_step "Testing external access..."
if curl -f http://$DOMAIN &>/dev/null; then
    print_success "✅ Website is accessible via HTTP"
else
    print_warning "⚠️  Website not accessible via HTTP - check DNS"
fi

# 11. Display final information
echo ""
echo "🎉 Simple Deployment completed!"
echo ""
echo "📋 Application Details:"
echo "🌐 URL: http://$DOMAIN"
echo "👤 Admin Email: admin@$DOMAIN"
echo "🔑 Admin Password: admin123"
echo ""
echo "🔧 Management Commands:"
echo "  📊 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs $APP_NAME"
echo "  🔄 Restart App: pm2 restart $APP_NAME"
echo ""
echo "🆘 Troubleshooting:"
echo "  📋 App Logs: pm2 logs $APP_NAME --lines 50"
echo "  🔍 Test Local: curl http://localhost"
echo "  🔍 Test Remote: curl http://$DOMAIN"
echo ""
if [ "$APP_RESPONDING" = true ]; then
    echo "✅ Your Lead App is running directly at: http://$DOMAIN"
    echo "🌐 No nginx, no custom ports - direct web access!"
else
    echo "⚠️  Your Lead App deployment completed but may need troubleshooting"
    echo "   Check the logs above and run: pm2 logs $APP_NAME"
fi
echo ""
echo "⚠️  IMPORTANT: Change the admin password after first login!"
echo "📝 Note: App runs directly on port 80 - no nginx needed!"
