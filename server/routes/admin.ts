import express from 'express';
import { eq, desc } from 'drizzle-orm';
import db from '../database/connection.js';
import { jsonData, users, leads } from '../database/schema.js';
import { authenticateToken, requireAdmin, AuthRequest } from '../middleware/auth.js';

const router = express.Router();

// Apply authentication and admin check to all routes
router.use(authenticateToken);
router.use(requireAdmin);

// Get all JSON data uploads
router.get('/json-data', async (req: AuthRequest, res) => {
  try {
    const allJsonData = await db
      .select({
        id: jsonData.id,
        fileName: jsonData.fileName,
        dataType: jsonData.dataType,
        recordCount: jsonData.recordCount,
        uploadedAt: jsonData.uploadedAt,
        userName: users.name,
        userEmail: users.email,
      })
      .from(jsonData)
      .leftJoin(users, eq(jsonData.userId, users.id))
      .orderBy(desc(jsonData.uploadedAt));

    res.json(allJsonData);
  } catch (error) {
    console.error('Error fetching JSON data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get specific JSON data by ID
router.get('/json-data/:id', async (req: AuthRequest, res) => {
  try {
    const dataId = parseInt(req.params.id);
    
    const data = await db
      .select()
      .from(jsonData)
      .where(eq(jsonData.id, dataId))
      .get();

    if (!data) {
      return res.status(404).json({ error: 'JSON data not found' });
    }

    // Parse the original data back to JSON
    const parsedData = JSON.parse(data.originalData);

    res.json({
      ...data,
      originalData: parsedData,
    });
  } catch (error) {
    console.error('Error fetching JSON data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all users (admin only)
router.get('/users', async (req: AuthRequest, res) => {
  try {
    const allUsers = await db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        role: users.role,
        createdAt: users.createdAt,
      })
      .from(users)
      .orderBy(desc(users.createdAt));

    res.json(allUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all leads from all users (admin only)
router.get('/all-leads', async (req: AuthRequest, res) => {
  try {
    const allLeads = await db
      .select({
        lead: leads,
        userName: users.name,
        userEmail: users.email,
      })
      .from(leads)
      .leftJoin(users, eq(leads.userId, users.id))
      .orderBy(desc(leads.createdAt));

    res.json(allLeads);
  } catch (error) {
    console.error('Error fetching all leads:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete JSON data (admin only)
router.delete('/json-data/:id', async (req: AuthRequest, res) => {
  try {
    const dataId = parseInt(req.params.id);
    
    const deletedData = await db
      .delete(jsonData)
      .where(eq(jsonData.id, dataId))
      .returning();

    if (deletedData.length === 0) {
      return res.status(404).json({ error: 'JSON data not found' });
    }

    res.json({ message: 'JSON data deleted successfully' });
  } catch (error) {
    console.error('Error deleting JSON data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
