import express from 'express';
import { eq, and, desc } from 'drizzle-orm';
import db from '../database/connection.js';
import { leads, jsonData } from '../database/schema.js';
import { authenticateToken, AuthRequest } from '../middleware/auth.js';

const router = express.Router();

// Get all leads for authenticated user
router.get('/', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userLeads = await db.select().from(leads).where(eq(leads.userId, req.userId!));
    res.json(userLeads);
  } catch (error) {
    console.error('Error fetching leads:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new lead
router.post('/', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const leadData = req.body;

    const newLead = await db.insert(leads).values({
      ...leadData,
      userId: req.userId!,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    res.status(201).json(newLead[0]);
  } catch (error) {
    console.error('Error creating lead:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Bulk create leads (for JSON upload)
router.post('/bulk', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { leads: leadsData, fileName, originalData } = req.body;

    if (!Array.isArray(leadsData)) {
      return res.status(400).json({ error: 'Leads must be an array' });
    }

    const leadsToInsert = leadsData.map(lead => ({
      ...lead,
      userId: req.userId!,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    // Start a transaction to insert both leads and JSON data
    const newLeads = await db.insert(leads).values(leadsToInsert).returning();

    // Save the original JSON data for admin access
    await db.insert(jsonData).values({
      userId: req.userId!,
      fileName: fileName || 'uploaded_data.json',
      originalData: JSON.stringify(originalData || leadsData),
      dataType: 'leads',
      recordCount: leadsData.length,
      uploadedAt: new Date(),
    });

    res.status(201).json({
      message: `Successfully created ${newLeads.length} leads`,
      leads: newLeads,
    });
  } catch (error) {
    console.error('Error creating bulk leads:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update lead
router.put('/:id', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const leadId = parseInt(req.params.id);
    const leadData = req.body;

    const updatedLead = await db.update(leads)
      .set({ ...leadData, updatedAt: new Date() })
      .where(and(eq(leads.id, leadId), eq(leads.userId, req.userId!)))
      .returning();

    if (updatedLead.length === 0) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    res.json(updatedLead[0]);
  } catch (error) {
    console.error('Error updating lead:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Mark lead as contacted
router.patch('/:id/contacted', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const leadId = parseInt(req.params.id);
    const { contacted } = req.body;

    const updatedLead = await db.update(leads)
      .set({ contacted: contacted, updatedAt: new Date() })
      .where(and(eq(leads.id, leadId), eq(leads.userId, req.userId!)))
      .returning();

    if (updatedLead.length === 0) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    res.json(updatedLead[0]);
  } catch (error) {
    console.error('Error updating lead contacted status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete lead
router.delete('/:id', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const leadId = parseInt(req.params.id);

    const deletedLead = await db.delete(leads)
      .where(and(eq(leads.id, leadId), eq(leads.userId, req.userId!)))
      .returning();

    if (deletedLead.length === 0) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    res.json({ message: 'Lead deleted successfully' });
  } catch (error) {
    console.error('Error deleting lead:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user's JSON data uploads
router.get('/uploads', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userUploads = await db
      .select({
        id: jsonData.id,
        fileName: jsonData.fileName,
        dataType: jsonData.dataType,
        recordCount: jsonData.recordCount,
        uploadedAt: jsonData.uploadedAt,
      })
      .from(jsonData)
      .where(eq(jsonData.userId, req.userId!))
      .orderBy(desc(jsonData.uploadedAt));

    res.json(userUploads);
  } catch (error) {
    console.error('Error fetching user uploads:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Load leads from a specific upload
router.post('/load-from-upload/:uploadId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const uploadId = parseInt(req.params.uploadId);

    // Get the JSON data
    const upload = await db
      .select()
      .from(jsonData)
      .where(and(eq(jsonData.id, uploadId), eq(jsonData.userId, req.userId!)))
      .get();

    if (!upload) {
      return res.status(404).json({ error: 'Upload not found' });
    }

    // Parse the original data
    const originalData = JSON.parse(upload.originalData);
    let leadsToLoad = Array.isArray(originalData) ? originalData : [originalData];

    // Clear existing leads for this user (optional - you might want to keep them)
    // await db.delete(leads).where(eq(leads.userId, req.userId!));

    // Insert the leads
    const leadsToInsert = leadsToLoad.map(lead => ({
      ...lead,
      userId: req.userId!,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    const newLeads = await db.insert(leads).values(leadsToInsert).returning();

    res.json({
      message: `Successfully loaded ${newLeads.length} leads from ${upload.fileName}`,
      leads: newLeads,
    });
  } catch (error) {
    console.error('Error loading leads from upload:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
