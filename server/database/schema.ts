import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name').notNull(),
  role: text('role').notNull().default('user'), // 'user' or 'admin'
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

export const leads = sqliteTable('leads', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: integer('user_id').notNull().references(() => users.id),
  name: text('name'),
  email: text('email'),
  mobilenumber: text('mobilenumber'),
  website: text('website'),
  latitude: text('latitude'),
  longitude: text('longitude'),
  address: text('address'),
  city: text('city'),
  state: text('state'),
  postalCode: text('postal_code'),
  country: text('country'),
  category: text('category'),
  rating: text('rating'),
  reviewCount: text('review_count'),
  closingHour: text('closing_hour'),
  facebookprofile: text('facebookprofile'),
  twitterprofile: text('twitterprofile'),
  linkedinprofile: text('linkedinprofile'),
  instagramprofile: text('instagramprofile'),
  businessDescription: text('business_description'),
  contacted: integer('contacted', { mode: 'boolean' }).default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

// New table for storing JSON data uploads for admin
export const jsonData = sqliteTable('json_data', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: integer('user_id').notNull().references(() => users.id),
  fileName: text('file_name').notNull(),
  originalData: text('original_data').notNull(), // Store the original JSON as text
  dataType: text('data_type').notNull(), // 'leads' or other types
  recordCount: integer('record_count').notNull().default(0),
  uploadedAt: integer('uploaded_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Lead = typeof leads.$inferSelect;
export type NewLead = typeof leads.$inferInsert;
export type JsonData = typeof jsonData.$inferSelect;
export type NewJsonData = typeof jsonData.$inferInsert;
