# 🚀 Lead App - Complete Lead Management System

A production-ready web application for uploading, managing, and tracking JSON lead data with authentication, admin dashboard, and database persistence.

## ✨ Features

### 🔐 **Authentication & Security**
- Secure user registration and login system
- JWT-based authentication
- Role-based access control (Admin/User)
- Password hashing with bcrypt

### 📊 **Lead Management**
- JSON file upload with drag & drop
- Smart data processing for various JSON structures
- Lead tracking (contacted/not contacted)
- Advanced search and filtering
- Export functionality
- Previous uploads history

### 👨‍💼 **Admin Dashboard**
- Complete admin panel
- User management
- System overview
- Data analytics

### 💾 **Database & Persistence**
- SQLite database with Drizzle ORM
- Automatic database setup
- Data persistence across sessions
- Backup-friendly structure

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: SQLite + Drizzle ORM
- **Authentication**: JWT + bcryptjs
- **UI Components**: shadcn/ui + <PERSON>dix UI
- **Process Management**: PM2
- **Web Server**: nginx with SSL

## 🚀 Production Deployment

### Quick Installation

```bash
# 1. Upload and extract your project
unzip lead-json-display-main.zip
cd lead-json-display-main

# 2. Make deploy script executable
chmod +x deploy.sh

# 3. Run the complete deployment (as root)
sudo ./deploy.sh
```

### What the Deploy Script Does

✅ **System Setup**
- Stops conflicting web servers (LiteSpeed, Apache)
- Installs Node.js 18, nginx, certbot, PM2
- Updates system packages

✅ **Application Setup**
- Installs dependencies
- Builds the application
- Creates database and admin user
- Configures environment variables

✅ **Server Configuration**
- Configures nginx for your domain
- Obtains free SSL certificate
- Sets up auto-restart on reboot
- Configures firewall (if available)

✅ **Testing & Diagnostics**
- Tests application startup
- Verifies HTTP/HTTPS access
- Provides detailed status information
- Shows troubleshooting commands

## 🌐 After Deployment

Your app will be running at:
- **HTTPS**: `https://lead.cybernox.tech` ✅
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `admin123`

⚠️ **IMPORTANT: Change the admin password after first login!**

## 🔧 Management Commands

```bash
# Check app status
pm2 status

# View app logs
pm2 logs lead-app

# Restart app
pm2 restart lead-app

# Check SSL certificate
sudo certbot certificates

# View nginx logs
sudo tail -f /var/log/nginx/lead.cybernox.tech.error.log
```

## 🆘 Troubleshooting

If you encounter issues:

1. **Check app logs**: `pm2 logs lead-app --lines 50`
2. **Check nginx**: `systemctl status nginx`
3. **Test locally**: `curl http://localhost:3001`
4. **Test remotely**: `curl http://lead.cybernox.tech`
5. **Restart services**: `pm2 restart lead-app && systemctl restart nginx`

## 🎯 Features Ready to Use

After deployment, you'll have:

- ✅ User authentication system
- ✅ JSON lead upload and processing
- ✅ Lead management and tracking
- ✅ Mark leads as contacted
- ✅ Previous uploads history
- ✅ Admin dashboard
- ✅ Database persistence
- ✅ HTTPS security
- ✅ Auto-restart on server reboot

## 📱 Using the App

1. **Visit** `https://lead.cybernox.tech`
2. **Login** with admin credentials
3. **Upload** your JSON lead files
4. **Manage** and track your leads
5. **Mark** leads as contacted
6. **View** previous uploads

## 🔄 Updates

To update the app:
1. Upload new version
2. Extract and replace files
3. Run `sudo ./deploy.sh` again

**Perfect for production use!** 🚀

---

**Need help?** Check the deployment logs or the troubleshooting section above.
