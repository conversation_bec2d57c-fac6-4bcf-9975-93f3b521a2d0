export interface User {
  id: number;
  email: string;
  name: string;
  role: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

export interface JsonDataRecord {
  id: number;
  fileName: string;
  dataType: string;
  recordCount: number;
  uploadedAt: string;
  userName?: string;
  userEmail?: string;
  originalData?: any;
}
