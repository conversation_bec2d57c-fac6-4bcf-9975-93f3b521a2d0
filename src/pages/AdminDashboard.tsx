import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { JsonDataRecord } from '@/types/user';
import { toast } from '@/components/ui/sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Eye, Trash2, Users, Database, FileText } from 'lucide-react';
import UserNav from '@/components/auth/UserNav';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [jsonData, setJsonData] = useState<JsonDataRecord[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [allLeads, setAllLeads] = useState<any[]>([]);
  const [selectedData, setSelectedData] = useState<JsonDataRecord | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user?.role === 'admin') {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [jsonDataResult, usersResult, leadsResult] = await Promise.all([
        apiService.getJsonData(),
        apiService.getAllUsers(),
        apiService.getAllLeads(),
      ]);
      
      setJsonData(jsonDataResult);
      setUsers(usersResult);
      setAllLeads(leadsResult);
    } catch (error) {
      console.error('Error loading admin data:', error);
      toast.error('Failed to load admin data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewData = async (id: number) => {
    try {
      const data = await apiService.getJsonDataById(id);
      setSelectedData(data);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch data details');
    }
  };

  const handleDeleteData = async (id: number) => {
    if (!confirm('Are you sure you want to delete this JSON data?')) return;
    
    try {
      await apiService.deleteJsonData(id);
      toast.success('JSON data deleted successfully');
      loadData();
    } catch (error) {
      console.error('Error deleting data:', error);
      toast.error('Failed to delete data');
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">You need admin privileges to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100">
      <header className="bg-white p-6 shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-lead-dark">
              <span className="text-lead-primary">Admin</span> Dashboard
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage users, data uploads, and system overview
            </p>
          </div>
          <UserNav />
        </div>
      </header>

      <main className="container mx-auto p-6 max-w-7xl">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">JSON Uploads</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{jsonData.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{allLeads.length}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="json-data" className="space-y-4">
          <TabsList>
            <TabsTrigger value="json-data">JSON Data Uploads</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="leads">All Leads</TabsTrigger>
          </TabsList>

          <TabsContent value="json-data" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>JSON Data Uploads</CardTitle>
                <CardDescription>
                  View and manage all JSON data uploaded by users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>File Name</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Records</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {jsonData.map((data) => (
                      <TableRow key={data.id}>
                        <TableCell className="font-medium">{data.fileName}</TableCell>
                        <TableCell>{data.userName} ({data.userEmail})</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{data.recordCount} records</Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(data.uploadedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleViewData(data.id)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>JSON Data: {selectedData?.fileName}</DialogTitle>
                                  <DialogDescription>
                                    Original data uploaded by {selectedData?.userName}
                                  </DialogDescription>
                                </DialogHeader>
                                {selectedData && (
                                  <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                                    {JSON.stringify(selectedData.originalData, null, 2)}
                                  </pre>
                                )}
                              </DialogContent>
                            </Dialog>
                            
                            <Button 
                              variant="destructive" 
                              size="sm"
                              onClick={() => handleDeleteData(data.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Users</CardTitle>
                <CardDescription>
                  All registered users in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Registration Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(user.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="leads" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Leads</CardTitle>
                <CardDescription>
                  All leads from all users in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground mb-4">
                  Showing {allLeads.length} total leads across all users
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Lead Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Created Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allLeads.slice(0, 50).map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {item.lead.name || item.lead.Name || 'Unnamed Lead'}
                        </TableCell>
                        <TableCell>
                          {item.lead.email || item.lead.Email || 'No email'}
                        </TableCell>
                        <TableCell>{item.userName} ({item.userEmail})</TableCell>
                        <TableCell>
                          {new Date(item.lead.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {allLeads.length > 50 && (
                  <div className="text-sm text-muted-foreground mt-4">
                    Showing first 50 leads. Total: {allLeads.length}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default AdminDashboard;
