
import React, { useState, useEffect } from 'react';
import {
  FileUploader,
  LeadDisplay,
  EmptyState,
  FilterControls,
  PreviousUploads
} from '@/components/leads';
import { toast } from '@/components/ui/sonner';
import { Lead } from '@/types/lead';
import { apiService } from '@/services/api';
import UserNav from '@/components/auth/UserNav';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

const Index = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // Load leads from database on component mount
  useEffect(() => {
    loadLeads();
  }, []);

  const loadLeads = async () => {
    try {
      setIsLoading(true);
      const dbLeads = await apiService.getLeads();
      setLeads(dbLeads);
      setFilteredLeads(dbLeads);
    } catch (error) {
      console.error('Error loading leads:', error);
      toast.error('Failed to load leads from database');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsLoading(true);

    // Create a FileReader to read the JSON file
    const reader = new FileReader();

    reader.onload = async (event) => {
      try {
        if (event.target?.result) {
          const parsedData = JSON.parse(event.target.result as string);

          let leadsToUpload: any[] = [];

          // Validate if the data is an array
          if (Array.isArray(parsedData)) {
            leadsToUpload = parsedData;
          } else if (typeof parsedData === 'object') {
            // Handle case where JSON is an object with arrays inside
            leadsToUpload = extractLeadsFromObject(parsedData);
          } else {
            toast.error('Invalid JSON structure. Expected an array of leads or an object containing leads.');
            setIsLoading(false);
            return;
          }

          // Save leads to database
          try {
            const response = await apiService.createLeadsBulk(
              leadsToUpload,
              file.name,
              parsedData
            );
            await loadLeads(); // Reload leads from database
            toast.success(`Successfully saved ${response.leads.length} leads to database`);
          } catch (error) {
            console.error('Error saving leads to database:', error);
            toast.error('Failed to save leads to database');
          }
        }
      } catch (error) {
        console.error('Error parsing JSON file:', error);
        toast.error('Error parsing the JSON file. Please check the format.');
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      toast.error('Error reading the file');
      setIsLoading(false);
    };

    reader.readAsText(file);
  };

  // Function to extract leads from different JSON structures
  const extractLeadsFromObject = (data: Record<string, any>): Lead[] => {
    // Look for arrays in the object that might contain leads
    for (const key in data) {
      if (Array.isArray(data[key]) && data[key].length > 0) {
        // Check if array items have common lead properties
        const firstItem = data[key][0];
        if (
          firstItem &&
          typeof firstItem === 'object' &&
          (firstItem.name || firstItem.email || firstItem.phone || firstItem.website)
        ) {
          return data[key];
        }
      }
    }

    // If no arrays found, try to convert the object itself into a lead
    if (data.name || data.email || data.phone || data.website) {
      return [data];
    }

    return [];
  };

  const handleFilter = (searchTerm: string, field: string | null) => {
    if (!searchTerm && !field) {
      setFilteredLeads(leads);
      return;
    }

    let filtered = [...leads];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(lead => {
        return Object.entries(lead).some(([key, value]) => {
          if (typeof value === 'string') {
            return value.toLowerCase().includes(term);
          }
          return false;
        });
      });
    }

    // Apply field filter
    if (field && field !== 'all') {
      filtered = filtered.filter(lead => lead[field] !== undefined && lead[field] !== null);
    }

    setFilteredLeads(filtered);
  };

  // Get leads based on contacted status
  const getAllLeads = () => filteredLeads;
  const getContactedLeads = () => filteredLeads.filter(lead => lead.contacted);
  const getUncontactedLeads = () => filteredLeads.filter(lead => !lead.contacted);

  // Get counts for badges
  const contactedCount = leads.filter(lead => lead.contacted).length;
  const uncontactedCount = leads.filter(lead => !lead.contacted).length;

  // Handle loading leads from previous uploads
  const handleLoadFromUpload = async (uploadId: number, fileName: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.loadLeadsFromUpload(uploadId);
      await loadLeads(); // Reload all leads from database
      toast.success(`Successfully loaded leads from ${fileName}`);
    } catch (error) {
      console.error('Error loading from upload:', error);
      toast.error('Failed to load leads from upload');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100">
      <header className="bg-white p-6 shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-lead-dark">
              <span className="text-lead-primary">JSON</span> Lead Display
            </h1>
            <p className="text-muted-foreground mt-1">
              Upload a JSON file to display and manage your leads
            </p>
          </div>
          <UserNav />
        </div>
      </header>

      <main className="container mx-auto p-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <FileUploader onFileUpload={handleFileUpload} isLoading={isLoading} />
          </div>
          <div>
            <PreviousUploads onLoadUpload={handleLoadFromUpload} />
          </div>
        </div>

        {leads.length > 0 ? (
          <>
            <FilterControls onFilter={handleFilter} leads={leads} />

            <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All Leads
                  <Badge variant="secondary" className="text-xs">
                    {leads.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="contacted" className="flex items-center gap-2">
                  Contacted
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                    {contactedCount}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="uncontacted" className="flex items-center gap-2">
                  Not Contacted
                  <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                    {uncontactedCount}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-6">
                <LeadDisplay
                  leads={getAllLeads()}
                  isLoading={isLoading}
                  onLeadUpdate={loadLeads}
                />
              </TabsContent>

              <TabsContent value="contacted" className="mt-6">
                <LeadDisplay
                  leads={getContactedLeads()}
                  isLoading={isLoading}
                  onLeadUpdate={loadLeads}
                />
                {getContactedLeads().length === 0 && (
                  <div className="text-center p-8">
                    <p className="text-lg text-muted-foreground">No contacted leads yet.</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Mark leads as contacted by checking the checkbox on each lead card.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="uncontacted" className="mt-6">
                <LeadDisplay
                  leads={getUncontactedLeads()}
                  isLoading={isLoading}
                  onLeadUpdate={loadLeads}
                />
                {getUncontactedLeads().length === 0 && (
                  <div className="text-center p-8">
                    <p className="text-lg text-muted-foreground">All leads have been contacted!</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Great job on following up with all your leads.
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EmptyState />
            <PreviousUploads onLoadUpload={handleLoadFromUpload} />
          </div>
        )}
      </main>
    </div>
  );
};

export default Index;
