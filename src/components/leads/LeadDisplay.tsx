import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import { Lead } from '@/types/lead';
import { Mail, Phone, Link, User, MapPin, Instagram, Facebook, Check } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { apiService } from '@/services/api';

interface LeadDisplayProps {
  leads: Lead[];
  isLoading: boolean;
  onLeadUpdate?: () => void;
}

export const LeadDisplay = ({ leads, isLoading, onLeadUpdate }: LeadDisplayProps) => {
  const [updatingLeads, setUpdatingLeads] = useState<Set<number>>(new Set());

  const handleContactedChange = async (lead: Lead) => {
    if (!lead.id) {
      toast.error('Cannot update lead: missing ID');
      return;
    }

    const newContactedStatus = !lead.contacted;
    setUpdatingLeads(prev => new Set(prev).add(lead.id!));

    try {
      await apiService.markLeadAsContacted(lead.id, newContactedStatus);

      // Display a toast notification
      if (newContactedStatus) {
        toast.success(`Marked "${lead.Name || 'lead'}" as contacted`);
      } else {
        toast.info(`Removed contacted status from "${lead.Name || 'lead'}"`);
      }

      // Trigger refresh of leads data
      if (onLeadUpdate) {
        onLeadUpdate();
      }
    } catch (error) {
      console.error('Error updating lead contacted status:', error);
      toast.error('Failed to update lead status');
    } finally {
      setUpdatingLeads(prev => {
        const newSet = new Set(prev);
        newSet.delete(lead.id!);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array(6).fill(0).map((_, i) => (
          <Card key={i} className="overflow-hidden animate-pulse">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <Skeleton className="h-4 w-4/6" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (leads.length === 0) {
    return (
      <div className="text-center p-8">
        <p className="text-lg text-muted-foreground">No leads found matching your filters.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {leads.map((lead, index) => (
        <Card
          key={lead.id || index}
          className={`overflow-hidden transition-all hover:shadow-md animate-fade-in ${
            lead.contacted ? 'border-green-200 bg-green-50' : ''
          }`}
        >
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <h3 className="text-xl font-medium flex items-center gap-2">
                    <User className="h-4 w-4 text-lead-primary" />
                    {lead.Name || 'Unnamed Lead'}
                    {lead.contacted && (
                      <Badge variant="default" className="text-xs bg-green-600">
                        <Check className="h-3 w-3 mr-1" />
                        Contacted
                      </Badge>
                    )}
                  </h3>
                  {lead.category && (
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {lead.category}
                      </Badge>
                      {lead.rating && (
                        <Badge variant="outline" className="text-xs">
                          {lead.rating} ⭐ {lead.reviewCount || ''}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>

                {/* Contacted Checkbox */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`contacted-${lead.id || index}`}
                    checked={lead.contacted || false}
                    onCheckedChange={() => handleContactedChange(lead)}
                    disabled={updatingLeads.has(lead.id || 0)}
                    className="border-lead-primary data-[state=checked]:bg-lead-primary"
                  />
                  <label
                    htmlFor={`contacted-${lead.id || index}`}
                    className="text-xs font-medium text-muted-foreground cursor-pointer select-none"
                  >
                    {updatingLeads.has(lead.id || 0) ? 'Updating...' : 'Contacted'}
                  </label>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                {lead.address && (
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-lead-primary mt-0.5" />
                    <div>
                      <p className="text-muted-foreground">{lead.address}</p>
                      {lead.City && lead.State && (
                        <p className="text-muted-foreground">{lead.City}, {lead.State} {lead.PostalCode}</p>
                      )}
                      {lead.Country && <p className="text-muted-foreground">{lead.Country}</p>}
                    </div>
                  </div>
                )}

                {lead.Email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-lead-primary" />
                    <a
                      href={`mailto:${lead.Email}`}
                      className="text-muted-foreground hover:text-foreground hover:underline"
                    >
                      {lead.Email}
                    </a>
                  </div>
                )}

                {lead.mobilenumber && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-lead-primary" />
                    <a
                      href={`tel:${lead.mobilenumber}`}
                      className="text-muted-foreground hover:text-foreground hover:underline"
                    >
                      {lead.mobilenumber}
                    </a>
                  </div>
                )}

                {lead.website && (
                  <div className="flex items-center gap-2">
                    <Link className="h-4 w-4 text-lead-primary" />
                    <a
                      href={lead.website.startsWith('http') ? lead.website : `https://${lead.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground hover:underline truncate max-w-[200px]"
                    >
                      {lead.website}
                    </a>
                  </div>
                )}

                {/* Social Media Links */}
                <div className="flex gap-2 pt-1">
                  {lead.Facebookprofile && (
                    <a
                      href={lead.Facebookprofile}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground"
                      title="Facebook"
                    >
                      <Facebook className="h-4 w-4" />
                    </a>
                  )}
                  {lead.instagramprofile && (
                    <a
                      href={lead.instagramprofile}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground"
                      title="Instagram"
                    >
                      <Instagram className="h-4 w-4" />
                    </a>
                  )}
                </div>
              </div>

              {/* Business Hours */}
              {lead.ClosingHour && (
                <div className="pt-2 border-t border-gray-100">
                  <p className="text-xs text-muted-foreground mb-1">Business Hours</p>
                  <p className="text-xs text-muted-foreground whitespace-pre-line">
                    {lead.ClosingHour}
                  </p>
                </div>
              )}

              {/* Additional Fields */}
              <div className="pt-2 border-t border-gray-100">
                <p className="text-xs text-muted-foreground mb-1">Additional Information</p>
                <div className="grid grid-cols-1 gap-1">
                  {Object.entries(lead)
                    .filter(([key, value]) =>
                      !['Name', 'Email', 'mobilenumber', 'website', 'address',
                       'City', 'State', 'PostalCode', 'Country', 'category',
                       'rating', 'reviewCount', 'ClosingHour', 'Facebookprofile',
                       'Twitterprofile', 'linkedinprofile', 'instagramprofile',
                       'BusinessDescription', 'Latitude', 'Longitude', 'contacted'].includes(key) &&
                      typeof value !== 'object' &&
                      typeof value !== 'function' &&
                      value !== null &&
                      value !== ""
                    )
                    .slice(0, 5)
                    .map(([key, value]) => (
                      <div key={key} className="text-xs">
                        <span className="font-medium capitalize">{key}:</span>{' '}
                        <span className="text-muted-foreground">
                          {String(value).substring(0, 35)}
                          {String(value).length > 35 ? '...' : ''}
                        </span>
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
