
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/sonner';
import { UploadCloud, FileJson } from 'lucide-react';

interface FileUploaderProps {
  onFileUpload: (file: File) => void;
  isLoading: boolean;
}

export const FileUploader = ({ onFileUpload, isLoading }: FileUploaderProps) => {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndUploadFile(e.dataTransfer.files[0]);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    
    if (e.target.files && e.target.files[0]) {
      validateAndUploadFile(e.target.files[0]);
    }
  };

  const validateAndUploadFile = (file: File) => {
    // Check if file is a JSON file
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
      toast.error('Please upload a valid JSON file');
      return;
    }
    
    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size exceeds 5MB limit');
      return;
    }
    
    onFileUpload(file);
  };

  return (
    <Card className={`transition-all duration-300 ${dragActive ? 'border-lead-primary ring-2 ring-lead-light' : ''}`}>
      <CardHeader>
        <CardTitle>Upload JSON File</CardTitle>
        <CardDescription>
          Drag and drop your JSON file or click to browse
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer"
          onDragEnter={handleDrag}
          onDragOver={handleDrag}
          onDragLeave={handleDrag}
          onDrop={handleDrop}
        >
          <Input 
            id="file-upload" 
            type="file" 
            accept=".json" 
            onChange={handleChange} 
            className="hidden"
          />
          <label 
            htmlFor="file-upload" 
            className="cursor-pointer flex flex-col items-center justify-center space-y-3"
          >
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-10 w-10 rounded-full border-t-2 border-b-2 border-lead-primary"></div>
                <p>Processing your file...</p>
              </div>
            ) : (
              <>
                <div className="h-16 w-16 rounded-full bg-lead-light/20 flex items-center justify-center">
                  {dragActive ? (
                    <FileJson className="h-8 w-8 text-lead-primary" />
                  ) : (
                    <UploadCloud className="h-8 w-8 text-lead-primary" />
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {dragActive ? "Drop the file here" : "Click to browse or drag and drop your JSON file"}
                </p>
                <Button variant="outline" className="mt-2" disabled={isLoading}>
                  Select JSON File
                </Button>
              </>
            )}
          </label>
        </div>
      </CardContent>
    </Card>
  );
};
