
import React from 'react';
import { File<PERSON><PERSON> } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export const EmptyState = () => {
  return (
    <Card className="border-dashed">
      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
        <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
          <FileJson className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-xl font-medium mb-2">No Leads Yet</h3>
        <p className="text-muted-foreground max-w-md mx-auto">
          Upload a JSON file containing your leads to visualize and manage them.
          Your file should contain an array of objects with lead information.
        </p>
        
        <div className="mt-8 p-4 bg-muted rounded-md text-left max-w-lg w-full overflow-auto">
          <pre className="text-xs text-muted-foreground">
{`// Example JSON format:
[
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "website": "www.example.com",
    "company": "ACME Corp",
    "status": "prospect"
  },
  ...
]`}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
};
