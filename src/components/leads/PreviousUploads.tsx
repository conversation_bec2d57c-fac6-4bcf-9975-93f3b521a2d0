import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { JsonDataRecord } from '@/types/user';
import { apiService } from '@/services/api';
import { toast } from '@/components/ui/sonner';
import { FileText, Calendar, Download, RefreshCw } from 'lucide-react';

interface PreviousUploadsProps {
  onLoadUpload: (uploadId: number, fileName: string) => void;
}

export const PreviousUploads: React.FC<PreviousUploadsProps> = ({ onLoadUpload }) => {
  const [uploads, setUploads] = useState<JsonDataRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingUploadId, setLoadingUploadId] = useState<number | null>(null);

  useEffect(() => {
    loadUploads();
  }, []);

  const loadUploads = async () => {
    try {
      setIsLoading(true);
      const userUploads = await apiService.getUserUploads();
      setUploads(userUploads);
    } catch (error) {
      console.error('Error loading uploads:', error);
      toast.error('Failed to load previous uploads');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadUpload = async (uploadId: number, fileName: string) => {
    try {
      setLoadingUploadId(uploadId);
      await onLoadUpload(uploadId, fileName);
    } catch (error) {
      console.error('Error loading upload:', error);
      toast.error('Failed to load upload');
    } finally {
      setLoadingUploadId(null);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Previous Uploads</CardTitle>
          <CardDescription>Loading your previous JSON uploads...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Skeleton className="h-8 w-8 rounded" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (uploads.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Previous Uploads</CardTitle>
          <CardDescription>No previous uploads found</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">
              Upload your first JSON file to see it here for future reference.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Previous Uploads</CardTitle>
            <CardDescription>
              Load leads from your previously uploaded JSON files
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={loadUploads}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {uploads.map((upload) => (
            <div
              key={upload.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="h-8 w-8 rounded bg-lead-primary/10 flex items-center justify-center">
                  <FileText className="h-4 w-4 text-lead-primary" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium text-sm">{upload.fileName}</p>
                    <Badge variant="secondary" className="text-xs">
                      {upload.recordCount} records
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {new Date(upload.uploadedAt).toLocaleDateString()} at{' '}
                      {new Date(upload.uploadedAt).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </span>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleLoadUpload(upload.id, upload.fileName)}
                disabled={loadingUploadId === upload.id}
                className="flex items-center space-x-1"
              >
                {loadingUploadId === upload.id ? (
                  <>
                    <RefreshCw className="h-3 w-3 animate-spin" />
                    <span>Loading...</span>
                  </>
                ) : (
                  <>
                    <Download className="h-3 w-3" />
                    <span>Load</span>
                  </>
                )}
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
