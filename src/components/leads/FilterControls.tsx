
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Lead } from '@/types/lead';
import { Search } from 'lucide-react';

interface FilterControlsProps {
  onFilter: (searchTerm: string, field: string | null) => void;
  leads: Lead[];
}

export const FilterControls = ({ onFilter, leads }: FilterControlsProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [commonFields, setCommonFields] = useState<string[]>([]);

  useEffect(() => {
    // Extract common fields from the leads for the dropdown
    if (leads.length > 0) {
      const fieldsMap: Record<string, number> = {};
      
      leads.forEach(lead => {
        Object.keys(lead).forEach(key => {
          fieldsMap[key] = (fieldsMap[key] || 0) + 1;
        });
      });
      
      // Get fields that appear in at least 30% of leads
      const minOccurrence = Math.max(1, Math.floor(leads.length * 0.3));
      const fields = Object.entries(fieldsMap)
        .filter(([_, count]) => count >= minOccurrence)
        .map(([field]) => field)
        .sort();
      
      setCommonFields(fields);
    }
  }, [leads]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onFilter(value, selectedField);
  };

  const handleFieldChange = (value: string) => {
    const field = value === 'all' ? null : value;
    setSelectedField(field);
    onFilter(searchTerm, field);
  };

  return (
    <div className="mb-6 flex flex-col sm:flex-row gap-4">
      <div className="relative flex-grow">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search leads..."
          value={searchTerm}
          onChange={handleSearchChange}
          className="pl-10"
        />
      </div>
      
      <div className="w-full sm:w-52">
        <Select onValueChange={handleFieldChange} defaultValue="all">
          <SelectTrigger>
            <SelectValue placeholder="Filter by field" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Fields</SelectItem>
            {commonFields.map(field => (
              <SelectItem key={field} value={field} className="capitalize">
                {field}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
