import { AuthResponse, LoginRequest, RegisterRequest, JsonDataRecord } from '@/types/user';
import { Lead } from '@/types/lead';

const API_BASE_URL = '/api';

class ApiService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Login failed');
    }

    return response.json();
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Registration failed');
    }

    return response.json();
  }

  // Leads endpoints
  async getLeads(): Promise<Lead[]> {
    const response = await fetch(`${API_BASE_URL}/leads`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch leads');
    }

    return response.json();
  }

  async createLead(lead: Partial<Lead>): Promise<Lead> {
    const response = await fetch(`${API_BASE_URL}/leads`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(lead),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create lead');
    }

    return response.json();
  }

  async createLeadsBulk(leads: Partial<Lead>[], fileName?: string, originalData?: any): Promise<{ message: string; leads: Lead[] }> {
    const response = await fetch(`${API_BASE_URL}/leads/bulk`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ leads, fileName, originalData }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create leads');
    }

    return response.json();
  }

  async updateLead(id: number, lead: Partial<Lead>): Promise<Lead> {
    const response = await fetch(`${API_BASE_URL}/leads/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(lead),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update lead');
    }

    return response.json();
  }

  async markLeadAsContacted(id: number, contacted: boolean): Promise<Lead> {
    const response = await fetch(`${API_BASE_URL}/leads/${id}/contacted`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ contacted }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update lead contacted status');
    }

    return response.json();
  }

  async getUserUploads(): Promise<JsonDataRecord[]> {
    const response = await fetch(`${API_BASE_URL}/leads/uploads`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch uploads');
    }

    return response.json();
  }

  async loadLeadsFromUpload(uploadId: number): Promise<{ message: string; leads: Lead[] }> {
    const response = await fetch(`${API_BASE_URL}/leads/load-from-upload/${uploadId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to load leads from upload');
    }

    return response.json();
  }

  async deleteLead(id: number): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/leads/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete lead');
    }
  }

  // Admin endpoints
  async getJsonData(): Promise<JsonDataRecord[]> {
    const response = await fetch(`${API_BASE_URL}/admin/json-data`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch JSON data');
    }

    return response.json();
  }

  async getJsonDataById(id: number): Promise<JsonDataRecord> {
    const response = await fetch(`${API_BASE_URL}/admin/json-data/${id}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch JSON data');
    }

    return response.json();
  }

  async deleteJsonData(id: number): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/json-data/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete JSON data');
    }
  }

  async getAllUsers(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/admin/users`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch users');
    }

    return response.json();
  }

  async getAllLeads(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/admin/all-leads`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch all leads');
    }

    return response.json();
  }
}

export const apiService = new ApiService();
