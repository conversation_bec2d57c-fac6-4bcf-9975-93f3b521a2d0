# Environment Configuration Example
# Copy this file to .env and update the values

# Server Configuration
NODE_ENV=production
PORT=3001

# Security
JWT_SECRET=your-super-secure-jwt-secret-change-this-in-production

# Database
DATABASE_URL=./server/database/database.sqlite

# Admin User (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrator

# Optional: External Database (if not using SQLite)
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=leadapp
# DATABASE_USER=leadapp_user
# DATABASE_PASSWORD=secure_password
