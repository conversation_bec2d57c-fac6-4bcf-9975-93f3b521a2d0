# 🚀 Lead App - Production Deployment Guide

## 📦 One-Command Installation

Deploy your complete Lead App with HTTPS in minutes!

### 🎯 Prerequisites

- **VPS** with Ubuntu 20.04+ or similar Linux
- **Root access** (sudo)
- **Domain** `lead.cybernox.tech` pointing to your VPS IP
- **Clean server** (script will handle conflicts)

### 🚀 Installation Steps

1. **Upload** your project zip to VPS
2. **Extract** and navigate to directory
3. **Run** the deployment script

```bash
# 1. Upload and extract your project
unzip lead-json-display-main.zip
cd lead-json-display-main

# 2. Make deploy script executable
chmod +x deploy.sh

# 3. Run the complete deployment (as root)
sudo ./deploy.sh
```

**That's it!** ✨

The script will automatically handle everything including stopping conflicting web servers.

## 🎉 What Happens Automatically

The script will:

✅ **Install System Dependencies**
- Node.js 18
- nginx web server
- SSL certificate tools (certbot)
- PM2 process manager

✅ **Setup Your App**
- Install project dependencies
- Build the application
- Create database and admin user
- Configure environment variables

✅ **Configure Server**
- Setup nginx for your domain
- Configure firewall (if available)
- Obtain free SSL certificate
- Setup auto-restart on reboot

## 🌐 After Installation

Your app will be running at:
- **HTTPS**: `https://lead.cybernox.tech` ✅
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `admin123`

⚠️ **IMPORTANT: Change the admin password after first login!**

## 🔧 Management Commands

```bash
# Check app status
pm2 status

# View app logs
pm2 logs lead-app

# Restart app
pm2 restart lead-app

# Check SSL certificate
sudo certbot certificates

# View nginx logs
sudo tail -f /var/log/nginx/lead.cybernox.tech.error.log
```

## 🆘 Troubleshooting

If something goes wrong:

1. **Check the deployment logs** - the script shows detailed output
2. **Verify DNS** - make sure `lead.cybernox.tech` points to your VPS IP
3. **Check app logs** - `pm2 logs lead-app`
4. **Check nginx** - `sudo systemctl status nginx`

## 🎯 Features Ready to Use

After installation, you'll have:

- ✅ User authentication system
- ✅ JSON lead upload and processing
- ✅ Lead management and tracking
- ✅ Mark leads as contacted
- ✅ Previous uploads history
- ✅ Admin dashboard
- ✅ Database persistence
- ✅ HTTPS security
- ✅ Auto-restart on server reboot

## 📱 Using the App

1. **Visit** `https://lead.cybernox.tech`
2. **Login** with admin credentials
3. **Upload** your JSON lead files
4. **Manage** and track your leads
5. **Mark** leads as contacted
6. **View** previous uploads

## 🔄 Updates

To update the app:
1. Upload new version
2. Extract and replace files
3. Run `sudo ./deploy.sh` again

**Perfect for production use!** 🚀

---

**Need help?** Check the deployment logs or contact support.
