#!/bin/bash

echo "🚀 FIXED Lead App Deployment"
echo "🌐 Deploying to: https://lead.cybernox.tech"
echo "🔧 All errors fixed and optimized"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Configuration
DOMAIN="lead.cybernox.tech"
APP_NAME="lead-app"
APP_PORT="3001"
PROJECT_DIR="$(pwd)"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

print_info "Starting FIXED deployment for $DOMAIN"
print_info "Project directory: $PROJECT_DIR"
echo ""

# 1. Stop any existing processes
print_step "Stopping existing processes..."
pm2 delete $APP_NAME 2>/dev/null || true
systemctl stop nginx 2>/dev/null || true
systemctl stop lsws 2>/dev/null || true
pkill lshttpd 2>/dev/null || true
print_success "Existing processes stopped"

# 2. Install system dependencies
print_step "Installing system dependencies..."
apt update
apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates

# Install Node.js 18 if not installed
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi

# Install nginx
if ! command -v nginx &> /dev/null; then
    apt install -y nginx
    systemctl enable nginx
fi

# Install PM2
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
fi

print_success "System dependencies installed"

# 3. Fix missing TypeScript config files
print_step "Creating missing config files..."

# Create tsconfig.app.json
cat > tsconfig.app.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
EOF

# Create tsconfig.node.json
cat > tsconfig.node.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "noEmit": true
  },
  "include": ["vite.config.ts"]
}
EOF

# Create missing vite config files
cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
  },
})
EOF

print_success "Config files created"

# 4. Install dependencies
print_step "Installing project dependencies..."
npm install --production=false
print_success "Dependencies installed"

# 5. Fix server route patterns
print_step "Fixing server route patterns..."
sed -i "s/app\.get('\*'/app.get('\/\*'/g" server/index.ts
print_success "Route patterns fixed"

# 6. Fix database connection
print_step "Fixing database connection..."
cat > server/database/connection.ts << 'EOF'
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create database connection
const sqlite = new Database(path.join(__dirname, 'database.sqlite'));
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('foreign_keys = ON');

// Create drizzle instance
export const db = drizzle(sqlite, { schema });

// Initialize database (no migrations needed)
export function initializeDatabase() {
  try {
    const result = sqlite.prepare('SELECT 1 as test').get();
    if (result?.test === 1) {
      console.log('✅ Database connection initialized');
    } else {
      throw new Error('Database connection test failed');
    }
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export default db;
EOF
print_success "Database connection fixed"

# 7. Build application
print_step "Building application..."
npm run build
print_success "Application built"

# 8. Setup database
print_step "Setting up database..."
npm run setup
print_success "Database setup complete"

# 9. Create environment file
print_step "Creating environment configuration..."
cat > .env << EOF
NODE_ENV=production
PORT=$APP_PORT
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_EMAIL=admin@$DOMAIN
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrator
EOF
print_success "Environment configured"

# 10. Configure nginx
print_step "Configuring nginx..."
rm -f /etc/nginx/sites-enabled/default

cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    root $PROJECT_DIR/dist;
    index index.html;
    
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:$APP_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    client_max_body_size 50M;
}
EOF

ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
nginx -t
systemctl start nginx
print_success "Nginx configured"

# 11. Start application
print_step "Starting application..."
pm2 start npm --name $APP_NAME -- run server:prod
pm2 save
pm2 startup systemd -u root --hp /root
print_success "Application started"

# 12. Test application
print_step "Testing application..."
sleep 10

for i in {1..5}; do
    if curl -f http://localhost:$APP_PORT/api/health &>/dev/null; then
        print_success "✅ Application responding on port $APP_PORT"
        break
    else
        print_warning "Attempt $i: Waiting for app to start..."
        sleep 5
    fi
done

# 13. Setup SSL
print_step "Setting up SSL certificate..."
apt install -y certbot python3-certbot-nginx
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN --redirect || {
    print_warning "SSL setup failed, but app is running on HTTP"
}

# 14. Final test
print_step "Final testing..."
if curl -f http://$DOMAIN &>/dev/null; then
    print_success "✅ Website accessible via HTTP"
else
    print_warning "⚠️  Website not accessible via HTTP"
fi

if curl -f https://$DOMAIN &>/dev/null; then
    print_success "✅ Website accessible via HTTPS"
else
    print_warning "⚠️  Website not accessible via HTTPS"
fi

echo ""
echo "🎉 FIXED Deployment completed!"
echo ""
echo "📋 Application Details:"
echo "🌐 URL: https://$DOMAIN"
echo "👤 Admin Email: admin@$DOMAIN"
echo "🔑 Admin Password: admin123"
echo ""
echo "🔧 Management Commands:"
echo "  📊 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs $APP_NAME"
echo "  🔄 Restart App: pm2 restart $APP_NAME"
echo ""
echo "✅ All errors fixed - Your Lead App is now running!"
echo "⚠️  IMPORTANT: Change the admin password after first login!"
